#include "cr10_hardware_interface/cr10_hardware_interface.h"
#include <urdf/model.h>

namespace cr10_hardware_interface
{

CR10HardwareInterface::CR10HardwareInterface(ros::NodeHandle& nh)
    : nh_(nh)
    , initialized_(false)
    , robot_connected_(false)
    , robot_enabled_(false)
    , control_frequency_(125.0)
    , position_tolerance_(0.01)
    , use_servo_j_(true)
    , servo_j_time_(0.008)
    , servo_j_lookahead_time_(0.1)
    , servo_j_gain_(300.0)
    , command_timeout_(ros::Duration(1.0))
{
    // Initialize joint names for CR10 (6-DOF)
    joint_names_ = {"joint1", "joint2", "joint3", "joint4", "joint5", "joint6"};
    
    // Resize vectors
    const size_t num_joints = joint_names_.size();
    joint_position_.resize(num_joints, 0.0);
    joint_velocity_.resize(num_joints, 0.0);
    joint_effort_.resize(num_joints, 0.0);
    joint_position_command_.resize(num_joints, 0.0);
    joint_position_command_prev_.resize(num_joints, 0.0);
}

CR10HardwareInterface::~CR10HardwareInterface()
{
    if (commander_) {
        disableRobot();
    }
}

bool CR10HardwareInterface::init()
{
    ROS_INFO("Initializing CR10 Hardware Interface...");

    // Load parameters
    if (!loadParameters()) {
        ROS_ERROR("Failed to load parameters");
        return false;
    }

    // Create commander
    try {
        commander_ = std::make_shared<CR5Commander>(robot_ip_);
        commander_->init();
        
        // Wait for connection
        ros::Duration(2.0).sleep();
        
        if (!commander_->isConnected()) {
            ROS_ERROR("Failed to connect to robot at IP: %s", robot_ip_.c_str());
            return false;
        }
        
        robot_connected_ = true;
        ROS_INFO("Connected to CR10 robot at IP: %s", robot_ip_.c_str());
        
    } catch (const std::exception& e) {
        ROS_ERROR("Failed to create commander: %s", e.what());
        return false;
    }

    // Register hardware interfaces
    if (!registerInterfaces()) {
        ROS_ERROR("Failed to register hardware interfaces");
        return false;
    }

    // Setup joint limits
    if (!setupJointLimits()) {
        ROS_ERROR("Failed to setup joint limits");
        return false;
    }

    // Read initial joint positions
    read(ros::Time::now(), ros::Duration(0.0));
    
    // Initialize command positions to current positions
    joint_position_command_ = joint_position_;
    joint_position_command_prev_ = joint_position_;

    initialized_ = true;
    last_command_time_ = ros::Time::now();
    
    ROS_INFO("CR10 Hardware Interface initialized successfully");
    return true;
}

bool CR10HardwareInterface::loadParameters()
{
    // Robot IP address
    if (!nh_.getParam("robot_ip", robot_ip_)) {
        robot_ip_ = "***********";  // Default CR10 IP
        ROS_WARN("robot_ip parameter not found, using default: %s", robot_ip_.c_str());
    }

    // Control frequency
    nh_.param("control_frequency", control_frequency_, 125.0);
    
    // Position tolerance
    nh_.param("position_tolerance", position_tolerance_, 0.01);
    
    // ServoJ parameters
    nh_.param("use_servo_j", use_servo_j_, true);
    nh_.param("servo_j_time", servo_j_time_, 0.008);
    nh_.param("servo_j_lookahead_time", servo_j_lookahead_time_, 0.1);
    nh_.param("servo_j_gain", servo_j_gain_, 300.0);
    
    // Command timeout
    double timeout_sec;
    nh_.param("command_timeout", timeout_sec, 1.0);
    command_timeout_ = ros::Duration(timeout_sec);

    ROS_INFO("Loaded parameters:");
    ROS_INFO("  robot_ip: %s", robot_ip_.c_str());
    ROS_INFO("  control_frequency: %.1f Hz", control_frequency_);
    ROS_INFO("  use_servo_j: %s", use_servo_j_ ? "true" : "false");
    ROS_INFO("  servo_j_time: %.3f s", servo_j_time_);

    return true;
}

bool CR10HardwareInterface::registerInterfaces()
{
    // Register joint state interface
    for (size_t i = 0; i < joint_names_.size(); ++i) {
        hardware_interface::JointStateHandle state_handle(
            joint_names_[i], 
            &joint_position_[i], 
            &joint_velocity_[i], 
            &joint_effort_[i]
        );
        joint_state_interface_.registerHandle(state_handle);
    }
    registerInterface(&joint_state_interface_);

    // Register position command interface
    for (size_t i = 0; i < joint_names_.size(); ++i) {
        hardware_interface::JointHandle position_handle(
            joint_state_interface_.getHandle(joint_names_[i]), 
            &joint_position_command_[i]
        );
        position_joint_interface_.registerHandle(position_handle);
    }
    registerInterface(&position_joint_interface_);

    ROS_INFO("Registered hardware interfaces for %zu joints", joint_names_.size());
    return true;
}

bool CR10HardwareInterface::setupJointLimits()
{
    // Load joint limits from URDF and parameter server
    urdf::Model urdf_model;
    if (!urdf_model.initParam("robot_description")) {
        ROS_WARN("Could not load URDF model, using default joint limits");
    }

    for (size_t i = 0; i < joint_names_.size(); ++i) {
        joint_limits_interface::JointLimits limits;
        joint_limits_interface::SoftJointLimits soft_limits;
        
        bool has_limits = false;
        bool has_soft_limits = false;

        // Try to get limits from URDF
        if (urdf_model.joints_.find(joint_names_[i]) != urdf_model.joints_.end()) {
            has_limits = joint_limits_interface::getJointLimits(
                urdf_model.joints_[joint_names_[i]], limits);
        }

        // Try to get limits from parameter server
        if (!has_limits) {
            has_limits = joint_limits_interface::getJointLimits(joint_names_[i], nh_, limits);
        }

        // Get soft limits from parameter server
        has_soft_limits = joint_limits_interface::getSoftJointLimits(joint_names_[i], nh_, soft_limits);

        if (has_limits) {
            // Register saturation interface
            joint_limits_interface::PositionJointSaturationHandle sat_handle(
                position_joint_interface_.getHandle(joint_names_[i]), limits);
            position_joint_limits_interface_.registerHandle(sat_handle);

            // Register soft limits interface if available
            if (has_soft_limits) {
                joint_limits_interface::PositionJointSoftLimitsHandle soft_handle(
                    position_joint_interface_.getHandle(joint_names_[i]), limits, soft_limits);
                position_joint_soft_limits_interface_.registerHandle(soft_handle);
            }

            ROS_INFO("Joint %s limits: [%.3f, %.3f] rad", 
                     joint_names_[i].c_str(), limits.min_position, limits.max_position);
        } else {
            ROS_WARN("No limits found for joint %s", joint_names_[i].c_str());
        }
    }

    return true;
}

void CR10HardwareInterface::read(const ros::Time& time, const ros::Duration& period)
{
    if (!commander_ || !robot_connected_) {
        return;
    }

    std::lock_guard<std::mutex> lock(state_mutex_);

    try {
        // Read joint positions from robot (in degrees)
        double joint_positions_deg[6];
        commander_->getCurrentJointStatus(joint_positions_deg);

        // Convert to radians and update joint states
        for (size_t i = 0; i < joint_names_.size(); ++i) {
            joint_position_[i] = deg2rad(joint_positions_deg[i]);
            // Note: CR10 doesn't provide velocity/effort feedback directly
            joint_velocity_[i] = 0.0;
            joint_effort_[i] = 0.0;
        }

        // Update robot status
        robot_enabled_ = commander_->isEnable();
        robot_connected_ = commander_->isConnected();

    } catch (const std::exception& e) {
        ROS_ERROR_THROTTLE(1.0, "Error reading from robot: %s", e.what());
        robot_connected_ = false;
    }
}

void CR10HardwareInterface::write(const ros::Time& time, const ros::Duration& period)
{
    if (!commander_ || !robot_connected_ || !robot_enabled_) {
        return;
    }

    std::lock_guard<std::mutex> lock(state_mutex_);

    try {
        // Enforce joint limits
        position_joint_limits_interface_.enforceLimits(period);
        position_joint_soft_limits_interface_.enforceLimits(period);

        // Check if commands have changed significantly
        if (commandsChanged()) {
            if (use_servo_j_) {
                sendServoJCommand();
            }
            
            // Update previous command
            joint_position_command_prev_ = joint_position_command_;
            last_command_time_ = time;
        }

    } catch (const std::exception& e) {
        ROS_ERROR_THROTTLE(1.0, "Error writing to robot: %s", e.what());
    }
}

bool CR10HardwareInterface::commandsChanged()
{
    for (size_t i = 0; i < joint_names_.size(); ++i) {
        if (std::abs(joint_position_command_[i] - joint_position_command_prev_[i]) > position_tolerance_) {
            return true;
        }
    }
    return false;
}

void CR10HardwareInterface::sendServoJCommand()
{
    try {
        // Convert joint positions from radians to degrees
        std::vector<double> joint_positions_deg(joint_names_.size());
        for (size_t i = 0; i < joint_names_.size(); ++i) {
            joint_positions_deg[i] = rad2deg(joint_position_command_[i]);
        }

        // Validate joint positions
        if (!validateJointPositions(joint_positions_deg)) {
            ROS_WARN_THROTTLE(1.0, "Joint positions out of range, skipping command");
            return;
        }

        // Send ServoJ command
        char cmd[200];
        snprintf(cmd, sizeof(cmd),
                "ServoJ(%.3f,%.3f,%.3f,%.3f,%.3f,%.3f,t=%.3f,lookahead_time=%.3f,gain=%.1f)",
                joint_positions_deg[0], joint_positions_deg[1], joint_positions_deg[2],
                joint_positions_deg[3], joint_positions_deg[4], joint_positions_deg[5],
                servo_j_time_, servo_j_lookahead_time_, servo_j_gain_);

        int32_t err_id;
        commander_->motionDoCmd(cmd, err_id);

        if (err_id != 0) {
            ROS_WARN_THROTTLE(1.0, "ServoJ command returned error: %d", err_id);
        }

    } catch (const std::exception& e) {
        ROS_ERROR_THROTTLE(1.0, "Error sending ServoJ command: %s", e.what());
    }
}

bool CR10HardwareInterface::validateJointPositions(const std::vector<double>& positions)
{
    // Basic range check for CR10 joints (in degrees)
    const std::vector<std::pair<double, double>> joint_limits = {
        {-360.0, 360.0},  // joint1
        {-135.0, 135.0},  // joint2
        {-135.0, 135.0},  // joint3
        {-360.0, 360.0},  // joint4
        {-135.0, 135.0},  // joint5
        {-360.0, 360.0}   // joint6
    };

    for (size_t i = 0; i < positions.size() && i < joint_limits.size(); ++i) {
        if (positions[i] < joint_limits[i].first || positions[i] > joint_limits[i].second) {
            ROS_WARN("Joint %zu position %.3f deg out of range [%.1f, %.1f]",
                     i, positions[i], joint_limits[i].first, joint_limits[i].second);
            return false;
        }
    }
    return true;
}

bool CR10HardwareInterface::isConnected() const
{
    std::lock_guard<std::mutex> lock(state_mutex_);
    return robot_connected_ && commander_ && commander_->isConnected();
}

bool CR10HardwareInterface::isEnabled() const
{
    std::lock_guard<std::mutex> lock(state_mutex_);
    return robot_enabled_ && commander_ && commander_->isEnable();
}

void CR10HardwareInterface::enableRobot()
{
    if (commander_) {
        try {
            // Send enable command using dashboard interface
            const char* cmd = "EnableRobot()";
            int32_t err_id;
            commander_->motionDoCmd(cmd, err_id);

            ros::Duration(1.0).sleep();  // Wait for enable
            robot_enabled_ = commander_->isEnable();

            if (err_id == 0) {
                ROS_INFO("Robot enable command sent successfully, status: %s",
                         robot_enabled_ ? "enabled" : "not enabled");
            } else {
                ROS_WARN("Robot enable command returned error: %d", err_id);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Error enabling robot: %s", e.what());
        }
    }
}

void CR10HardwareInterface::disableRobot()
{
    if (commander_) {
        try {
            // Send disable command using dashboard interface
            const char* cmd = "DisableRobot()";
            int32_t err_id;
            commander_->motionDoCmd(cmd, err_id);

            ros::Duration(0.5).sleep();  // Wait for disable
            robot_enabled_ = false;

            if (err_id == 0) {
                ROS_INFO("Robot disabled successfully");
            } else {
                ROS_WARN("Robot disable command returned error: %d", err_id);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Error disabling robot: %s", e.what());
        }
    }
}

void CR10HardwareInterface::clearError()
{
    if (commander_) {
        try {
            // Send clear error command using dashboard interface
            const char* cmd = "ClearError()";
            int32_t err_id;
            commander_->motionDoCmd(cmd, err_id);

            ros::Duration(0.5).sleep();  // Wait for clear

            if (err_id == 0) {
                ROS_INFO("Robot errors cleared successfully");
            } else {
                ROS_WARN("Clear error command returned error: %d", err_id);
            }
        } catch (const std::exception& e) {
            ROS_ERROR("Error clearing robot errors: %s", e.what());
        }
    }
}

} // namespace cr10_hardware_interface
