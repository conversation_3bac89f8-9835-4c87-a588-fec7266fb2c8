#include <ros/ros.h>
#include <sensor_msgs/JointState.h>
#include <trajectory_msgs/JointTrajectory.h>
#include <trajectory_msgs/JointTrajectoryPoint.h>
#include <control_msgs/FollowJointTrajectoryAction.h>
#include <actionlib/client/simple_action_client.h>
#include <chrono>
#include <vector>
#include <cmath>

class SynchronizationTester
{
public:
    SynchronizationTester(ros::NodeHandle& nh) : nh_(nh), joint_state_received_(false)
    {
        // Subscribe to joint states
        joint_state_sub_ = nh_.subscribe("/joint_states", 10, 
            &SynchronizationTester::jointStateCallback, this);
        
        // Publisher for trajectory commands
        trajectory_pub_ = nh_.advertise<trajectory_msgs::JointTrajectory>(
            "/cr10_position_controller/command", 10);
        
        // Wait for joint states
        ROS_INFO("Waiting for joint states...");
        while (ros::ok() && !joint_state_received_) {
            ros::spinOnce();
            ros::Duration(0.1).sleep();
        }
        
        if (joint_state_received_) {
            ROS_INFO("Joint states received, ready for synchronization testing");
        }
    }
    
    void testStateSync()
    {
        ROS_INFO("=== State Synchronization Test ===");
        
        // Record initial state
        ros::Time start_time = ros::Time::now();
        std::vector<double> initial_positions = current_joint_positions_;
        
        // Monitor state updates for 5 seconds
        int update_count = 0;
        double max_latency = 0.0;
        double total_latency = 0.0;
        
        ros::Time test_start = ros::Time::now();
        while ((ros::Time::now() - test_start).toSec() < 5.0) {
            ros::Time before_spin = ros::Time::now();
            ros::spinOnce();
            ros::Time after_spin = ros::Time::now();
            
            if (joint_state_updated_) {
                double latency = (after_spin - last_joint_state_time_).toSec() * 1000.0;
                max_latency = std::max(max_latency, latency);
                total_latency += latency;
                update_count++;
                joint_state_updated_ = false;
            }
            
            ros::Duration(0.001).sleep();  // 1ms sleep
        }
        
        double avg_latency = (update_count > 0) ? total_latency / update_count : 0.0;
        double frequency = update_count / 5.0;
        
        ROS_INFO("State sync results:");
        ROS_INFO("  Update frequency: %.1f Hz", frequency);
        ROS_INFO("  Average latency: %.2f ms", avg_latency);
        ROS_INFO("  Maximum latency: %.2f ms", max_latency);
        
        if (frequency > 100.0 && max_latency < 10.0) {
            ROS_INFO("✅ State synchronization: EXCELLENT");
        } else if (frequency > 50.0 && max_latency < 20.0) {
            ROS_INFO("⚠️ State synchronization: GOOD");
        } else {
            ROS_INFO("❌ State synchronization: POOR");
        }
    }
    
    void testCommandSync()
    {
        ROS_INFO("\n=== Command Synchronization Test ===");
        
        if (!joint_state_received_) {
            ROS_ERROR("No joint states available for command sync test");
            return;
        }
        
        // Create a small trajectory
        trajectory_msgs::JointTrajectory traj;
        traj.header.stamp = ros::Time::now() + ros::Duration(0.1);
        traj.joint_names = {"joint1", "joint2", "joint3", "joint4", "joint5", "joint6"};
        
        // Current position
        trajectory_msgs::JointTrajectoryPoint point1;
        point1.positions = current_joint_positions_;
        point1.time_from_start = ros::Duration(0.0);
        traj.points.push_back(point1);
        
        // Small movement (5 degrees on joint1)
        trajectory_msgs::JointTrajectoryPoint point2;
        point2.positions = current_joint_positions_;
        point2.positions[0] += 0.087;  // 5 degrees in radians
        point2.time_from_start = ros::Duration(2.0);
        traj.points.push_back(point2);
        
        // Return to original position
        trajectory_msgs::JointTrajectoryPoint point3;
        point3.positions = current_joint_positions_;
        point3.time_from_start = ros::Duration(4.0);
        traj.points.push_back(point3);
        
        ROS_INFO("Sending test trajectory...");
        trajectory_pub_.publish(traj);
        
        // Monitor execution
        ros::Time exec_start = ros::Time::now();
        std::vector<double> position_errors;
        double max_error = 0.0;
        
        while ((ros::Time::now() - exec_start).toSec() < 5.0) {
            ros::spinOnce();
            
            if (joint_state_received_) {
                // Calculate position error (simple check for joint1)
                double error = std::abs(current_joint_positions_[0] - point2.positions[0]);
                position_errors.push_back(error);
                max_error = std::max(max_error, error);
            }
            
            ros::Duration(0.01).sleep();
        }
        
        double avg_error = 0.0;
        if (!position_errors.empty()) {
            avg_error = std::accumulate(position_errors.begin(), position_errors.end(), 0.0) / position_errors.size();
        }
        
        ROS_INFO("Command sync results:");
        ROS_INFO("  Average position error: %.4f rad (%.2f deg)", avg_error, avg_error * 180.0 / M_PI);
        ROS_INFO("  Maximum position error: %.4f rad (%.2f deg)", max_error, max_error * 180.0 / M_PI);
        
        if (max_error < 0.01) {  // < 0.57 degrees
            ROS_INFO("✅ Command synchronization: EXCELLENT");
        } else if (max_error < 0.02) {  // < 1.15 degrees
            ROS_INFO("⚠️ Command synchronization: GOOD");
        } else {
            ROS_INFO("❌ Command synchronization: POOR");
        }
    }
    
    void testRealtimePerformance()
    {
        ROS_INFO("\n=== Real-time Performance Test ===");
        
        std::vector<double> loop_times;
        int loop_count = 0;
        
        ros::Time test_start = ros::Time::now();
        while ((ros::Time::now() - test_start).toSec() < 10.0) {
            auto loop_start = std::chrono::high_resolution_clock::now();
            
            ros::spinOnce();
            
            auto loop_end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::microseconds>(loop_end - loop_start);
            loop_times.push_back(duration.count() / 1000.0);  // Convert to milliseconds
            
            loop_count++;
            ros::Duration(0.008).sleep();  // 125 Hz target
        }
        
        if (!loop_times.empty()) {
            double avg_time = std::accumulate(loop_times.begin(), loop_times.end(), 0.0) / loop_times.size();
            double max_time = *std::max_element(loop_times.begin(), loop_times.end());
            double min_time = *std::min_element(loop_times.begin(), loop_times.end());
            
            // Calculate jitter (standard deviation)
            double variance = 0.0;
            for (double time : loop_times) {
                variance += (time - avg_time) * (time - avg_time);
            }
            double jitter = std::sqrt(variance / loop_times.size());
            
            ROS_INFO("Real-time performance results:");
            ROS_INFO("  Average loop time: %.2f ms", avg_time);
            ROS_INFO("  Minimum loop time: %.2f ms", min_time);
            ROS_INFO("  Maximum loop time: %.2f ms", max_time);
            ROS_INFO("  Jitter (std dev): %.2f ms", jitter);
            ROS_INFO("  Effective frequency: %.1f Hz", loop_count / 10.0);
            
            if (max_time < 8.0 && jitter < 1.0) {
                ROS_INFO("✅ Real-time performance: EXCELLENT");
            } else if (max_time < 15.0 && jitter < 2.0) {
                ROS_INFO("⚠️ Real-time performance: GOOD");
            } else {
                ROS_INFO("❌ Real-time performance: POOR");
            }
        }
    }
    
    void runAllTests()
    {
        ROS_INFO("Starting CR10 Synchronization and Real-time Tests");
        ROS_INFO("=" * 60);
        
        testStateSync();
        ros::Duration(1.0).sleep();
        
        testCommandSync();
        ros::Duration(1.0).sleep();
        
        testRealtimePerformance();
        
        ROS_INFO("\n" + std::string(60, '='));
        ROS_INFO("All synchronization tests completed");
    }

private:
    ros::NodeHandle& nh_;
    ros::Subscriber joint_state_sub_;
    ros::Publisher trajectory_pub_;
    
    bool joint_state_received_;
    bool joint_state_updated_;
    std::vector<double> current_joint_positions_;
    ros::Time last_joint_state_time_;
    
    void jointStateCallback(const sensor_msgs::JointState::ConstPtr& msg)
    {
        if (msg->position.size() >= 6) {
            current_joint_positions_ = std::vector<double>(msg->position.begin(), msg->position.begin() + 6);
            joint_state_received_ = true;
            joint_state_updated_ = true;
            last_joint_state_time_ = ros::Time::now();
        }
    }
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "cr10_sync_test");
    ros::NodeHandle nh("~");
    
    try {
        SynchronizationTester tester(nh);
        tester.runAllTests();
        
    } catch (const std::exception& e) {
        ROS_ERROR("Synchronization test failed: %s", e.what());
        return -1;
    }
    
    return 0;
}
