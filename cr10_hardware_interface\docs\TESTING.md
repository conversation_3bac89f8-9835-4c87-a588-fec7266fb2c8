# CR10 Hardware Interface Testing Guide

This document provides comprehensive testing procedures for the CR10 Hardware Interface.

## Test Overview

The testing suite includes multiple levels of validation:

1. **Network Connectivity Tests** - Basic network and TCP port accessibility
2. **Hardware Interface Tests** - Robot connection and communication
3. **Controller Tests** - ROS control framework integration
4. **Unit Tests** - Code-level validation

## Prerequisites

### Hardware Requirements
- CR10 robot with TCP/IP interface enabled
- Ethernet connection to robot controller
- Computer with ROS Melodic/Noetic installed

### Software Requirements
```bash
# Install required packages
sudo apt-get install ros-$ROS_DISTRO-ros-control
sudo apt-get install ros-$ROS_DISTRO-ros-controllers
sudo apt-get install ros-$ROS_DISTRO-joint-state-controller
sudo apt-get install ros-$ROS_DISTRO-position-controllers
```

### Network Configuration
Configure your network interface to match robot subnet:

```bash
# For LAN1 (***********)
sudo ip addr add ***********00/24 dev eth0

# For LAN2 (*************)  
sudo ip addr add *************00/24 dev eth0
```

## Quick Test Suite

### Automated Test Runner
Run all tests automatically:

```bash
# Make script executable
chmod +x ~/catkin_ws/src/cr10_hardware_interface/scripts/run_tests.py

# Run all tests
rosrun cr10_hardware_interface run_tests.py --robot-ip ***********

# Run specific test
rosrun cr10_hardware_interface run_tests.py --test network --robot-ip ***********
```

## Individual Test Procedures

### 1. Network Connectivity Test

Test basic network connectivity and TCP port accessibility:

```bash
# Using dedicated network test tool
rosrun cr10_hardware_interface network_test ***********

# Using launch file
roslaunch cr10_hardware_interface test_cr10_connection.launch robot_ip:=*********** test_type:=network
```

**Expected Output:**
```
✅ Ping successful - Network connectivity OK
✅ Port 29999 - Connected (15ms)
✅ Port 30003 - Connected (12ms)  
✅ Port 30004 - Connected (18ms)
🎉 All ports accessible! Robot should be ready for connection.
```

### 2. Hardware Interface Connection Test

Test robot connection and basic communication:

```bash
# Direct connection test
rosrun cr10_hardware_interface test_connection ***********

# Using launch file
roslaunch cr10_hardware_interface test_cr10_connection.launch robot_ip:=*********** test_type:=connection
```

**Expected Output:**
```
✅ Successfully connected to robot!
✅ Robot enable command sent successfully
✅ Joint reading test completed
✅ Control loop test completed
🎉 All tests completed successfully!
```

### 3. Full Hardware Interface Test

Test complete hardware interface with controllers:

```bash
# Launch full test environment
roslaunch cr10_hardware_interface test_cr10_connection.launch robot_ip:=*********** test_type:=full

# In another terminal, check topics
rostopic list
rostopic echo /joint_states

# Check controllers
rosservice call /controller_manager/list_controllers
```

### 4. Unit Tests

Run code-level unit tests:

```bash
# Build with tests
catkin_make tests

# Run unit tests
catkin_make run_tests_cr10_hardware_interface

# View test results
catkin_test_results
```

## Troubleshooting

### Network Issues

**Problem:** Ping fails
```bash
# Check network interface
ip addr show

# Check routing
ip route show

# Test with different IP
ping *************  # Try LAN2 if LAN1 fails
```

**Problem:** TCP ports not accessible
- Verify robot controller TCP/IP interface is enabled
- Check robot controller network settings
- Ensure robot is powered on and initialized

### Connection Issues

**Problem:** Hardware interface fails to connect
```bash
# Check ROS environment
echo $ROS_PACKAGE_PATH

# Verify package is built
rospack find cr10_hardware_interface

# Check for error messages
rosrun cr10_hardware_interface test_connection ***********
```

**Problem:** Robot won't enable
- Check robot safety systems (emergency stops, protective stops)
- Verify robot is in correct mode
- Check for active alarms on robot controller

### Controller Issues

**Problem:** Controllers fail to load
```bash
# Check controller configuration
rosparam list | grep controller

# Check hardware interface registration
rostopic echo /joint_states

# Manually spawn controller
rosrun controller_manager spawner joint_state_controller
```

## Test Results Interpretation

### Success Indicators
- ✅ All network tests pass
- ✅ Robot connection established
- ✅ Joint states published to `/joint_states`
- ✅ Controllers load successfully
- ✅ No error messages in logs

### Warning Indicators
- ⚠️ Robot connected but not enabled (check safety systems)
- ⚠️ Some controllers fail to load (check configuration)
- ⚠️ High network latency (>50ms)

### Failure Indicators
- ❌ Network connectivity fails (check cables/configuration)
- ❌ TCP ports not accessible (check robot settings)
- ❌ Hardware interface crashes (check logs)

## Performance Testing

### Latency Test
```bash
# Monitor control loop timing
rostopic hz /joint_states

# Expected: ~125 Hz for optimal performance
# Acceptable: >50 Hz minimum
```

### Load Test
```bash
# Run extended test
timeout 300 rosrun cr10_hardware_interface test_connection ***********

# Monitor system resources
htop
```

## Continuous Integration

For automated testing in CI/CD pipelines:

```bash
# Non-interactive test run
export DISPLAY=""
rosrun cr10_hardware_interface run_tests.py --robot-ip $ROBOT_IP --test all
```

## Test Data Collection

### Log Collection
```bash
# Enable detailed logging
export ROSCONSOLE_CONFIG_FILE=/path/to/debug.conf

# Run tests with logging
rosrun cr10_hardware_interface test_connection *********** 2>&1 | tee test_log.txt
```

### Performance Metrics
- Connection establishment time
- Joint state update frequency  
- Command response latency
- Error recovery time

## Safety Considerations

- Always ensure emergency stop is accessible during testing
- Test in a safe environment with adequate clearance
- Monitor robot behavior during all tests
- Have manual override capabilities ready

## Reporting Issues

When reporting test failures, include:

1. Test command used
2. Complete error output
3. Robot model and firmware version
4. Network configuration
5. ROS version and OS details
6. Hardware interface logs

## Next Steps

After successful testing:

1. Integrate with MoveIt configuration
2. Develop application-specific controllers
3. Implement safety monitoring
4. Performance optimization
5. Production deployment
