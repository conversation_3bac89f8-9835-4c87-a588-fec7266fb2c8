#include <ros/ros.h>
#include <controller_manager/controller_manager.h>
#include <signal.h>
#include "cr10_hardware_interface/cr10_hardware_interface.h"

// Global variables for signal handling
std::shared_ptr<cr10_hardware_interface::CR10HardwareInterface> g_hw_interface;
std::shared_ptr<controller_manager::ControllerManager> g_controller_manager;
bool g_shutdown_requested = false;

void signalHandler(int sig)
{
    ROS_INFO("Shutdown signal received (%d). Stopping CR10 hardware interface...", sig);
    g_shutdown_requested = true;
    
    if (g_hw_interface) {
        g_hw_interface->disableRobot();
    }
    
    ros::shutdown();
}

int main(int argc, char** argv)
{
    // Initialize ROS
    ros::init(argc, argv, "cr10_hardware_interface_node");
    ros::NodeHandle nh;
    ros::NodeHandle private_nh("~");

    // Setup signal handlers
    signal(S<PERSON>IN<PERSON>, signalHandler);
    signal(<PERSON><PERSON><PERSON><PERSON><PERSON>, signalHandler);

    ROS_INFO("Starting CR10 Hardware Interface Node...");

    try {
        // Create hardware interface
        g_hw_interface = std::make_shared<cr10_hardware_interface::CR10HardwareInterface>(private_nh);
        
        // Initialize hardware interface
        if (!g_hw_interface->init()) {
            ROS_ERROR("Failed to initialize CR10 hardware interface");
            return -1;
        }

        // Create controller manager
        g_controller_manager = std::make_shared<controller_manager::ControllerManager>(
            g_hw_interface.get(), nh);

        // Get control frequency
        double control_frequency = 125.0;  // Default 125 Hz
        private_nh.param("control_frequency", control_frequency, 125.0);
        
        if (control_frequency <= 0) {
            ROS_ERROR("Invalid control frequency: %.1f Hz", control_frequency);
            return -1;
        }

        ROS_INFO("Control frequency: %.1f Hz", control_frequency);
        ros::Duration control_period(1.0 / control_frequency);

        // Wait for robot connection
        ROS_INFO("Waiting for robot connection...");
        ros::Time start_time = ros::Time::now();
        ros::Duration timeout(10.0);  // 10 second timeout
        
        while (ros::ok() && !g_hw_interface->isConnected()) {
            if (ros::Time::now() - start_time > timeout) {
                ROS_ERROR("Timeout waiting for robot connection");
                return -1;
            }
            ros::Duration(0.1).sleep();
            ros::spinOnce();
        }

        if (!g_hw_interface->isConnected()) {
            ROS_ERROR("Robot not connected");
            return -1;
        }

        ROS_INFO("Robot connected successfully");

        // Enable robot
        ROS_INFO("Enabling robot...");
        g_hw_interface->enableRobot();
        
        // Wait a bit for enable to take effect
        ros::Duration(1.0).sleep();
        
        if (!g_hw_interface->isEnabled()) {
            ROS_WARN("Robot may not be enabled. Check robot status and safety systems.");
        } else {
            ROS_INFO("Robot enabled successfully");
        }

        // Main control loop
        ROS_INFO("Starting control loop...");
        ros::Time last_time = ros::Time::now();
        ros::Rate rate(control_frequency);

        while (ros::ok() && !g_shutdown_requested) {
            ros::Time current_time = ros::Time::now();
            ros::Duration elapsed_time = current_time - last_time;
            last_time = current_time;

            // Check connection status
            if (!g_hw_interface->isConnected()) {
                ROS_ERROR_THROTTLE(1.0, "Lost connection to robot");
                continue;
            }

            // Read from hardware
            g_hw_interface->read(current_time, elapsed_time);

            // Update controllers
            g_controller_manager->update(current_time, elapsed_time);

            // Write to hardware
            g_hw_interface->write(current_time, elapsed_time);

            // Process ROS callbacks
            ros::spinOnce();

            // Sleep to maintain control frequency
            rate.sleep();
        }

    } catch (const std::exception& e) {
        ROS_ERROR("Exception in main loop: %s", e.what());
        return -1;
    }

    // Cleanup
    ROS_INFO("Shutting down CR10 hardware interface...");
    
    if (g_hw_interface) {
        g_hw_interface->disableRobot();
        g_hw_interface.reset();
    }
    
    if (g_controller_manager) {
        g_controller_manager.reset();
    }

    ROS_INFO("CR10 Hardware Interface Node stopped");
    return 0;
}
