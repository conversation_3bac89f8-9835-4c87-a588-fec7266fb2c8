#include <ros/ros.h>
#include <std_msgs/Float64MultiArray.h>
#include <std_msgs/Int32.h>
#include <diagnostic_msgs/DiagnosticArray.h>
#include <diagnostic_msgs/DiagnosticStatus.h>
#include <diagnostic_msgs/KeyValue.h>
#include <chrono>
#include <vector>
#include <algorithm>

class RealtimeMonitor
{
public:
    RealtimeMonitor(ros::NodeHandle& nh) : nh_(nh), loop_count_(0)
    {
        // Publishers for monitoring data
        timing_pub_ = nh_.advertise<std_msgs::Float64MultiArray>("control_timing", 10);
        diagnostics_pub_ = nh_.advertise<diagnostic_msgs::DiagnosticArray>("diagnostics", 10);
        
        // Subscribers for joint states and control commands
        joint_state_sub_ = nh_.subscribe("/joint_states", 10, 
            &RealtimeMonitor::jointStateCallback, this);
        
        // Timer for periodic diagnostics
        diagnostics_timer_ = nh_.createTimer(ros::Duration(1.0), 
            &RealtimeMonitor::publishDiagnostics, this);
        
        // Initialize timing buffers
        timing_buffer_size_ = 1000;
        read_times_.reserve(timing_buffer_size_);
        write_times_.reserve(timing_buffer_size_);
        loop_times_.reserve(timing_buffer_size_);
        
        last_joint_state_time_ = ros::Time::now();
        
        ROS_INFO("Realtime monitor started");
    }

private:
    ros::NodeHandle& nh_;
    ros::Publisher timing_pub_;
    ros::Publisher diagnostics_pub_;
    ros::Subscriber joint_state_sub_;
    ros::Timer diagnostics_timer_;
    
    // Timing data
    std::vector<double> read_times_;
    std::vector<double> write_times_;
    std::vector<double> loop_times_;
    size_t timing_buffer_size_;
    int loop_count_;
    
    // Joint state monitoring
    ros::Time last_joint_state_time_;
    double joint_state_frequency_;
    
    void jointStateCallback(const sensor_msgs::JointState::ConstPtr& msg)
    {
        ros::Time current_time = ros::Time::now();
        
        if (last_joint_state_time_.isValid()) {
            double period = (current_time - last_joint_state_time_).toSec();
            if (period > 0.0) {
                joint_state_frequency_ = 1.0 / period;
            }
        }
        
        last_joint_state_time_ = current_time;
    }
    
    void publishDiagnostics(const ros::TimerEvent& event)
    {
        diagnostic_msgs::DiagnosticArray diag_array;
        diag_array.header.stamp = ros::Time::now();
        
        // Control loop diagnostics
        diagnostic_msgs::DiagnosticStatus control_status;
        control_status.name = "CR10 Control Loop";
        control_status.hardware_id = "cr10_hardware_interface";
        
        // Calculate timing statistics
        double avg_read_time = 0.0, avg_write_time = 0.0, avg_loop_time = 0.0;
        double max_read_time = 0.0, max_write_time = 0.0, max_loop_time = 0.0;
        
        if (!read_times_.empty()) {
            avg_read_time = std::accumulate(read_times_.begin(), read_times_.end(), 0.0) / read_times_.size();
            max_read_time = *std::max_element(read_times_.begin(), read_times_.end());
        }
        
        if (!write_times_.empty()) {
            avg_write_time = std::accumulate(write_times_.begin(), write_times_.end(), 0.0) / write_times_.size();
            max_write_time = *std::max_element(write_times_.begin(), write_times_.end());
        }
        
        if (!loop_times_.empty()) {
            avg_loop_time = std::accumulate(loop_times_.begin(), loop_times_.end(), 0.0) / loop_times_.size();
            max_loop_time = *std::max_element(loop_times_.begin(), loop_times_.end());
        }
        
        // Determine status level
        if (joint_state_frequency_ > 100.0 && max_loop_time < 10.0) {
            control_status.level = diagnostic_msgs::DiagnosticStatus::OK;
            control_status.message = "Control loop running optimally";
        } else if (joint_state_frequency_ > 50.0 && max_loop_time < 20.0) {
            control_status.level = diagnostic_msgs::DiagnosticStatus::WARN;
            control_status.message = "Control loop performance degraded";
        } else {
            control_status.level = diagnostic_msgs::DiagnosticStatus::ERROR;
            control_status.message = "Control loop performance critical";
        }
        
        // Add key-value pairs
        diagnostic_msgs::KeyValue kv;
        
        kv.key = "joint_state_frequency";
        kv.value = std::to_string(joint_state_frequency_);
        control_status.values.push_back(kv);
        
        kv.key = "avg_read_time_ms";
        kv.value = std::to_string(avg_read_time);
        control_status.values.push_back(kv);
        
        kv.key = "max_read_time_ms";
        kv.value = std::to_string(max_read_time);
        control_status.values.push_back(kv);
        
        kv.key = "avg_write_time_ms";
        kv.value = std::to_string(avg_write_time);
        control_status.values.push_back(kv);
        
        kv.key = "max_write_time_ms";
        kv.value = std::to_string(max_write_time);
        control_status.values.push_back(kv);
        
        kv.key = "avg_loop_time_ms";
        kv.value = std::to_string(avg_loop_time);
        control_status.values.push_back(kv);
        
        kv.key = "max_loop_time_ms";
        kv.value = std::to_string(max_loop_time);
        control_status.values.push_back(kv);
        
        diag_array.status.push_back(control_status);
        diagnostics_pub_.publish(diag_array);
        
        // Publish timing data
        std_msgs::Float64MultiArray timing_msg;
        timing_msg.data = {avg_read_time, max_read_time, avg_write_time, max_write_time, 
                          avg_loop_time, max_loop_time, joint_state_frequency_};
        timing_pub_.publish(timing_msg);
        
        // Clear buffers periodically to prevent memory growth
        if (read_times_.size() > timing_buffer_size_) {
            read_times_.clear();
            write_times_.clear();
            loop_times_.clear();
        }
        
        loop_count_++;
    }
};

int main(int argc, char** argv)
{
    ros::init(argc, argv, "cr10_realtime_monitor");
    ros::NodeHandle nh("~");
    
    try {
        RealtimeMonitor monitor(nh);
        
        ROS_INFO("CR10 Realtime Monitor running...");
        ros::spin();
        
    } catch (const std::exception& e) {
        ROS_ERROR("Realtime monitor failed: %s", e.what());
        return -1;
    }
    
    return 0;
}
