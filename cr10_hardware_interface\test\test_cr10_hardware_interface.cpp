#include <gtest/gtest.h>
#include <ros/ros.h>
#include <sensor_msgs/JointState.h>
#include <trajectory_msgs/JointTrajectory.h>
#include <control_msgs/FollowJointTrajectoryAction.h>
#include <actionlib/client/simple_action_client.h>
#include "cr10_hardware_interface/cr10_hardware_interface.h"

class CR10HardwareInterfaceTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        nh_.reset(new ros::NodeHandle("~"));
        
        // Set test parameters
        nh_->setParam("robot_ip", "***********");
        nh_->setParam("control_frequency", 10.0);  // Lower frequency for testing
        nh_->setParam("use_servo_j", true);
        nh_->setParam("position_tolerance", 0.01);
        
        // Create hardware interface
        hw_interface_.reset(new cr10_hardware_interface::CR10HardwareInterface(*nh_));
        
        // Subscribe to joint states
        joint_state_sub_ = nh_->subscribe("/joint_states", 1, 
            &CR10HardwareInterfaceTest::jointStateCallback, this);
        
        joint_state_received_ = false;
    }

    void TearDown() override
    {
        if (hw_interface_) {
            hw_interface_->disableRobot();
        }
    }

    void jointStateCallback(const sensor_msgs::JointState::ConstPtr& msg)
    {
        last_joint_state_ = *msg;
        joint_state_received_ = true;
    }

    bool waitForJointState(double timeout = 5.0)
    {
        ros::Time start_time = ros::Time::now();
        while (ros::ok() && !joint_state_received_) {
            ros::spinOnce();
            if ((ros::Time::now() - start_time).toSec() > timeout) {
                return false;
            }
            ros::Duration(0.01).sleep();
        }
        return joint_state_received_;
    }

    std::unique_ptr<ros::NodeHandle> nh_;
    std::unique_ptr<cr10_hardware_interface::CR10HardwareInterface> hw_interface_;
    ros::Subscriber joint_state_sub_;
    sensor_msgs::JointState last_joint_state_;
    bool joint_state_received_;
};

// Test basic initialization
TEST_F(CR10HardwareInterfaceTest, TestInitialization)
{
    EXPECT_FALSE(hw_interface_->isConnected());
    EXPECT_FALSE(hw_interface_->isEnabled());
}

// Test parameter loading
TEST_F(CR10HardwareInterfaceTest, TestParameterLoading)
{
    // This test will pass if init() doesn't crash due to parameter issues
    // Note: init() may fail due to no robot connection, but parameters should load
    bool init_attempted = true;
    try {
        hw_interface_->init();
    } catch (const std::exception& e) {
        // Expected if no robot is connected
        init_attempted = true;
    }
    EXPECT_TRUE(init_attempted);
}

// Test joint names and interface registration
TEST_F(CR10HardwareInterfaceTest, TestJointConfiguration)
{
    // Test that the hardware interface has the correct number of joints
    // This is tested indirectly through the interface registration
    EXPECT_NO_THROW(hw_interface_->init());
}

class CR10ConnectionTest : public ::testing::Test
{
protected:
    void SetUp() override
    {
        nh_.reset(new ros::NodeHandle("~"));
        
        // Get robot IP from parameter or use default
        std::string robot_ip;
        nh_->param<std::string>("robot_ip", robot_ip, "***********");
        
        nh_->setParam("robot_ip", robot_ip);
        nh_->setParam("control_frequency", 10.0);
        nh_->setParam("use_servo_j", true);
        
        hw_interface_.reset(new cr10_hardware_interface::CR10HardwareInterface(*nh_));
        
        // Subscribe to joint states
        joint_state_sub_ = nh_->subscribe("/joint_states", 1, 
            &CR10ConnectionTest::jointStateCallback, this);
        
        joint_state_received_ = false;
    }

    void TearDown() override
    {
        if (hw_interface_ && hw_interface_->isConnected()) {
            hw_interface_->disableRobot();
        }
    }

    void jointStateCallback(const sensor_msgs::JointState::ConstPtr& msg)
    {
        last_joint_state_ = *msg;
        joint_state_received_ = true;
    }

    bool waitForConnection(double timeout = 10.0)
    {
        ros::Time start_time = ros::Time::now();
        while (ros::ok()) {
            if (hw_interface_->isConnected()) {
                return true;
            }
            if ((ros::Time::now() - start_time).toSec() > timeout) {
                return false;
            }
            ros::Duration(0.1).sleep();
            ros::spinOnce();
        }
        return false;
    }

    bool waitForJointState(double timeout = 5.0)
    {
        ros::Time start_time = ros::Time::now();
        joint_state_received_ = false;
        while (ros::ok() && !joint_state_received_) {
            ros::spinOnce();
            if ((ros::Time::now() - start_time).toSec() > timeout) {
                return false;
            }
            ros::Duration(0.01).sleep();
        }
        return joint_state_received_;
    }

    std::unique_ptr<ros::NodeHandle> nh_;
    std::unique_ptr<cr10_hardware_interface::CR10HardwareInterface> hw_interface_;
    ros::Subscriber joint_state_sub_;
    sensor_msgs::JointState last_joint_state_;
    bool joint_state_received_;
};

// Test robot connection (requires actual robot)
TEST_F(CR10ConnectionTest, TestRobotConnection)
{
    EXPECT_TRUE(hw_interface_->init()) << "Failed to initialize hardware interface";
    EXPECT_TRUE(waitForConnection(10.0)) << "Failed to connect to robot within timeout";
    EXPECT_TRUE(hw_interface_->isConnected()) << "Robot should be connected";
}

// Test robot enable/disable (requires actual robot)
TEST_F(CR10ConnectionTest, TestRobotEnableDisable)
{
    ASSERT_TRUE(hw_interface_->init());
    ASSERT_TRUE(waitForConnection(10.0));
    
    // Test enable
    hw_interface_->enableRobot();
    ros::Duration(2.0).sleep();  // Wait for enable to take effect
    
    // Note: isEnabled() may return false if robot safety systems are not satisfied
    // This is expected behavior
    
    // Test disable
    hw_interface_->disableRobot();
    ros::Duration(1.0).sleep();
    
    // After disable, robot should not be enabled
    EXPECT_FALSE(hw_interface_->isEnabled());
}

// Test joint state reading (requires actual robot)
TEST_F(CR10ConnectionTest, TestJointStateReading)
{
    ASSERT_TRUE(hw_interface_->init());
    ASSERT_TRUE(waitForConnection(10.0));
    
    // Enable robot for joint state reading
    hw_interface_->enableRobot();
    ros::Duration(2.0).sleep();
    
    // Test reading joint states
    ros::Time start_time = ros::Time::now();
    ros::Duration period(0.1);
    
    for (int i = 0; i < 10; ++i) {
        hw_interface_->read(ros::Time::now(), period);
        ros::Duration(0.1).sleep();
    }
    
    // Joint states should be read without errors
    EXPECT_TRUE(hw_interface_->isConnected());
}

// Test basic control loop (requires actual robot)
TEST_F(CR10ConnectionTest, TestControlLoop)
{
    ASSERT_TRUE(hw_interface_->init());
    ASSERT_TRUE(waitForConnection(10.0));
    
    hw_interface_->enableRobot();
    ros::Duration(2.0).sleep();
    
    // Simulate control loop
    ros::Time current_time = ros::Time::now();
    ros::Duration period(0.1);
    
    for (int i = 0; i < 5; ++i) {
        // Read from hardware
        hw_interface_->read(current_time, period);
        
        // Write to hardware (commands should be initialized to current positions)
        hw_interface_->write(current_time, period);
        
        current_time += period;
        ros::Duration(0.1).sleep();
    }
    
    EXPECT_TRUE(hw_interface_->isConnected());
}

int main(int argc, char** argv)
{
    ros::init(argc, argv, "test_cr10_hardware_interface");
    testing::InitGoogleTest(&argc, argv);
    
    // Run tests
    int result = RUN_ALL_TESTS();
    
    ros::shutdown();
    return result;
}
