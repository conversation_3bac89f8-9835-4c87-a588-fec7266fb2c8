# CR10 Hardware Interface

ROS control hardware interface for the Dobot CR10 robot, providing seamless integration with the ROS control framework.

## Features

- **Real-time joint state feedback** - 125Hz control loop
- **Position control** - Using ServoJ for precise positioning
- **Joint limits enforcement** - Software safety limits
- **Standard ROS control interface** - Compatible with MoveIt and other ROS control tools
- **TCP/IP communication** - Direct connection to CR10 controller

## Prerequisites

- ROS Melodic/Noetic
- CR10 robot with TCP/IP interface enabled
- Network connection to robot controller

## Installation

1. Clone this package to your catkin workspace:
```bash
cd ~/catkin_ws/src
# This package should already be in your workspace
```

2. Install dependencies:
```bash
cd ~/catkin_ws
rosdep install --from-paths src --ignore-src -r -y
```

3. Build the package:
```bash
catkin_make
# or
catkin build cr10_hardware_interface
```

## Configuration

### Network Setup

1. Connect to the CR10 controller via Ethernet
2. Configure your network interface to be in the same subnet as the robot:
   - LAN1: 192.168.5.x (robot IP: ***********)
   - LAN2: 192.168.100.x (robot IP: *************)
   - WiFi: 192.168.1.x (robot IP: ***********)

### Robot Configuration

Ensure the robot controller has TCP/IP interface enabled and is in the correct mode.

## Usage

### Basic Usage

1. Launch the hardware interface:
```bash
roslaunch cr10_hardware_interface cr10_hardware_interface.launch robot_ip:=***********
```

2. The interface will automatically:
   - Connect to the robot
   - Enable the robot (if not already enabled)
   - Start the control loop
   - Load joint state and position controllers

### With Custom IP

```bash
roslaunch cr10_hardware_interface cr10_hardware_interface.launch robot_ip:=*************
```

### With Visualization

```bash
roslaunch cr10_hardware_interface cr10_hardware_interface.launch rviz:=true
```

## Controllers

The package provides several controller configurations:

### Joint Trajectory Controller (Default)
- **Name**: `cr10_position_controller`
- **Type**: `position_controllers/JointTrajectoryController`
- **Use case**: Trajectory execution, MoveIt integration

### Individual Joint Position Controllers
- **Names**: `joint1_position_controller`, `joint2_position_controller`, etc.
- **Type**: `position_controllers/JointPositionController`
- **Use case**: Individual joint control

### Joint State Controller
- **Name**: `joint_state_controller`
- **Type**: `joint_state_controller/JointStateController`
- **Use case**: Publishing joint states to `/joint_states` topic

## Topics

- `/joint_states` - Current joint positions, velocities, and efforts
- `/cr10_position_controller/command` - Trajectory commands
- `/cr10_position_controller/state` - Controller state

## Services

The hardware interface provides access to robot services through the underlying commander interface.

## Parameters

### Hardware Interface Parameters

- `robot_ip` (string, default: "***********") - Robot IP address
- `control_frequency` (double, default: 125.0) - Control loop frequency in Hz
- `use_servo_j` (bool, default: true) - Use ServoJ for position control
- `servo_j_time` (double, default: 0.008) - ServoJ time parameter
- `servo_j_lookahead_time` (double, default: 0.1) - ServoJ lookahead time
- `servo_j_gain` (double, default: 300.0) - ServoJ gain parameter
- `position_tolerance` (double, default: 0.01) - Position change threshold
- `command_timeout` (double, default: 1.0) - Command timeout in seconds

## Safety

- Joint limits are enforced in software
- Position commands are validated before sending to robot
- Connection monitoring with automatic reconnection
- Emergency stop capability through robot controller

## Troubleshooting

### Connection Issues

1. Check network connectivity:
```bash
ping ***********
```

2. Verify robot IP address and network configuration

3. Ensure robot controller TCP/IP interface is enabled

### Control Issues

1. Check robot status - ensure robot is enabled and not in error state
2. Verify joint limits in configuration files
3. Monitor ROS logs for error messages

### Performance Issues

1. Reduce control frequency if experiencing network delays
2. Adjust ServoJ parameters for smoother motion
3. Check network latency and stability

## Integration with MoveIt

This hardware interface is designed to work seamlessly with MoveIt. Use the existing CR10 MoveIt configuration and update the controller configuration to use this hardware interface.

## License

BSD License - see LICENSE file for details.
