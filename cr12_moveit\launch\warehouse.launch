<launch>

  <!-- The path to the database must be specified -->
  <arg name="moveit_warehouse_database_path" />

  <!-- Load warehouse parameters -->
  <include file="$(find cr12_moveit)/launch/warehouse_settings.launch.xml" />

  <!-- Run the DB server -->
  <node name="$(anon mongo_wrapper_ros)" cwd="ROS_HOME" type="mongo_wrapper_ros.py" pkg="warehouse_ros_mongo">
    <param name="overwrite" value="false"/>
    <param name="database_path" value="$(arg moveit_warehouse_database_path)" />
  </node>

</launch>
