#!/usr/bin/env python3

import os
import sys
import subprocess
import argparse
import time
import signal

class CR10TestRunner:
    def __init__(self, robot_ip="***********"):
        self.robot_ip = robot_ip
        self.processes = []
        
    def run_command(self, cmd, timeout=30):
        """Run a command with timeout"""
        print(f"Running: {cmd}")
        try:
            result = subprocess.run(cmd, shell=True, capture_output=True, 
                                  text=True, timeout=timeout)
            return result.returncode == 0, result.stdout, result.stderr
        except subprocess.TimeoutExpired:
            print(f"Command timed out after {timeout} seconds")
            return False, "", "Timeout"
    
    def test_network_connectivity(self):
        """Test basic network connectivity"""
        print("\n" + "="*50)
        print("NETWORK CONNECTIVITY TEST")
        print("="*50)
        
        # Test ping
        success, stdout, stderr = self.run_command(f"ping -c 3 -W 3 {self.robot_ip}")
        if success:
            print("✅ Ping test: PASSED")
        else:
            print("❌ Ping test: FAILED")
            print("   Check network connection and robot power")
            return False
        
        # Test TCP ports using netcat if available
        ports = [29999, 30003, 30004]
        for port in ports:
            success, _, _ = self.run_command(f"timeout 3 nc -z {self.robot_ip} {port}", timeout=5)
            if success:
                print(f"✅ Port {port}: ACCESSIBLE")
            else:
                print(f"❌ Port {port}: NOT ACCESSIBLE")
        
        return True
    
    def test_ros_environment(self):
        """Test ROS environment setup"""
        print("\n" + "="*50)
        print("ROS ENVIRONMENT TEST")
        print("="*50)
        
        # Check if ROS is sourced
        if 'ROS_PACKAGE_PATH' not in os.environ:
            print("❌ ROS environment not sourced")
            print("   Run: source /opt/ros/melodic/setup.bash")
            print("   Or:  source /opt/ros/noetic/setup.bash")
            return False
        
        print("✅ ROS environment: OK")
        
        # Check if package is built
        success, _, _ = self.run_command("rospack find cr10_hardware_interface")
        if success:
            print("✅ Package found: OK")
        else:
            print("❌ Package not found")
            print("   Run: catkin_make or catkin build")
            return False
        
        return True
    
    def test_hardware_interface(self):
        """Test hardware interface functionality"""
        print("\n" + "="*50)
        print("HARDWARE INTERFACE TEST")
        print("="*50)
        
        # Start roscore if not running
        success, _, _ = self.run_command("rostopic list", timeout=5)
        if not success:
            print("Starting roscore...")
            roscore_proc = subprocess.Popen(['roscore'], stdout=subprocess.DEVNULL, 
                                          stderr=subprocess.DEVNULL)
            self.processes.append(roscore_proc)
            time.sleep(3)
        
        # Run connection test
        cmd = f"timeout 30 rosrun cr10_hardware_interface test_connection {self.robot_ip}"
        success, stdout, stderr = self.run_command(cmd, timeout=35)
        
        if success:
            print("✅ Hardware interface test: PASSED")
            return True
        else:
            print("❌ Hardware interface test: FAILED")
            if stderr:
                print(f"Error: {stderr}")
            return False
    
    def test_controllers(self):
        """Test controller loading"""
        print("\n" + "="*50)
        print("CONTROLLER TEST")
        print("="*50)
        
        # Launch hardware interface with controllers
        launch_cmd = f"roslaunch cr10_hardware_interface test_cr10_connection.launch robot_ip:={self.robot_ip} test_type:=full"
        
        print("Starting hardware interface with controllers...")
        proc = subprocess.Popen(launch_cmd.split(), stdout=subprocess.PIPE, 
                               stderr=subprocess.PIPE)
        self.processes.append(proc)
        
        # Wait for startup
        time.sleep(10)
        
        # Check if joint_states topic is published
        success, stdout, stderr = self.run_command("timeout 5 rostopic echo /joint_states -n 1")
        if success:
            print("✅ Joint states topic: ACTIVE")
        else:
            print("❌ Joint states topic: NOT ACTIVE")
        
        # Check controller manager
        success, stdout, stderr = self.run_command("timeout 5 rosservice call /controller_manager/list_controllers")
        if success:
            print("✅ Controller manager: ACTIVE")
            if "joint_state_controller" in stdout:
                print("✅ Joint state controller: LOADED")
            else:
                print("❌ Joint state controller: NOT LOADED")
        else:
            print("❌ Controller manager: NOT ACTIVE")
        
        return success
    
    def run_all_tests(self):
        """Run all tests in sequence"""
        print("CR10 Hardware Interface Test Suite")
        print("=" * 60)
        print(f"Robot IP: {self.robot_ip}")
        
        tests = [
            ("ROS Environment", self.test_ros_environment),
            ("Network Connectivity", self.test_network_connectivity),
            ("Hardware Interface", self.test_hardware_interface),
            ("Controllers", self.test_controllers)
        ]
        
        results = {}
        
        for test_name, test_func in tests:
            try:
                results[test_name] = test_func()
            except Exception as e:
                print(f"❌ {test_name} test failed with exception: {e}")
                results[test_name] = False
            
            time.sleep(2)  # Brief pause between tests
        
        # Print summary
        print("\n" + "="*60)
        print("TEST SUMMARY")
        print("="*60)
        
        all_passed = True
        for test_name, passed in results.items():
            status = "✅ PASSED" if passed else "❌ FAILED"
            print(f"{test_name:.<30} {status}")
            if not passed:
                all_passed = False
        
        print("\n" + "="*60)
        if all_passed:
            print("🎉 ALL TESTS PASSED! CR10 hardware interface is ready.")
        else:
            print("⚠️ SOME TESTS FAILED. Check the output above for details.")
        
        return all_passed
    
    def cleanup(self):
        """Clean up processes"""
        print("\nCleaning up...")
        for proc in self.processes:
            try:
                proc.terminate()
                proc.wait(timeout=5)
            except:
                proc.kill()

def main():
    parser = argparse.ArgumentParser(description='CR10 Hardware Interface Test Suite')
    parser.add_argument('--robot-ip', default='***********', 
                       help='Robot IP address (default: ***********)')
    parser.add_argument('--test', choices=['network', 'ros', 'hardware', 'controllers', 'all'],
                       default='all', help='Test to run (default: all)')
    
    args = parser.parse_args()
    
    tester = CR10TestRunner(args.robot_ip)
    
    def signal_handler(sig, frame):
        print("\nInterrupted by user")
        tester.cleanup()
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    
    try:
        if args.test == 'all':
            success = tester.run_all_tests()
        elif args.test == 'network':
            success = tester.test_network_connectivity()
        elif args.test == 'ros':
            success = tester.test_ros_environment()
        elif args.test == 'hardware':
            success = tester.test_hardware_interface()
        elif args.test == 'controllers':
            success = tester.test_controllers()
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\nTest interrupted by user")
        sys.exit(1)
    finally:
        tester.cleanup()

if __name__ == '__main__':
    main()
