# Joint limits for CR10 robot
# All angles are in radians

joint_limits:
  joint1:
    has_position_limits: true
    min_position: -6.28318530718  # -360 degrees
    max_position: 6.28318530718   # +360 degrees
    has_velocity_limits: true
    max_velocity: 3.14159265359   # 180 deg/s
    has_acceleration_limits: true
    max_acceleration: 6.28318530718  # 360 deg/s^2
    has_effort_limits: true
    max_effort: 300.0

  joint2:
    has_position_limits: true
    min_position: -2.35619449019  # -135 degrees
    max_position: 2.35619449019   # +135 degrees
    has_velocity_limits: true
    max_velocity: 3.14159265359   # 180 deg/s
    has_acceleration_limits: true
    max_acceleration: 6.28318530718  # 360 deg/s^2
    has_effort_limits: true
    max_effort: 300.0

  joint3:
    has_position_limits: true
    min_position: -2.35619449019  # -135 degrees
    max_position: 2.35619449019   # +135 degrees
    has_velocity_limits: true
    max_velocity: 3.14159265359   # 180 deg/s
    has_acceleration_limits: true
    max_acceleration: 6.28318530718  # 360 deg/s^2
    has_effort_limits: true
    max_effort: 300.0

  joint4:
    has_position_limits: true
    min_position: -6.28318530718  # -360 degrees
    max_position: 6.28318530718   # +360 degrees
    has_velocity_limits: true
    max_velocity: 3.14159265359   # 180 deg/s
    has_acceleration_limits: true
    max_acceleration: 6.28318530718  # 360 deg/s^2
    has_effort_limits: true
    max_effort: 300.0

  joint5:
    has_position_limits: true
    min_position: -2.35619449019  # -135 degrees
    max_position: 2.35619449019   # +135 degrees
    has_velocity_limits: true
    max_velocity: 3.14159265359   # 180 deg/s
    has_acceleration_limits: true
    max_acceleration: 6.28318530718  # 360 deg/s^2
    has_effort_limits: true
    max_effort: 300.0

  joint6:
    has_position_limits: true
    min_position: -6.28318530718  # -360 degrees
    max_position: 6.28318530718   # +360 degrees
    has_velocity_limits: true
    max_velocity: 3.14159265359   # 180 deg/s
    has_acceleration_limits: true
    max_acceleration: 6.28318530718  # 360 deg/s^2
    has_effort_limits: true
    max_effort: 300.0

# Soft limits (optional - triggers warnings before hard limits)
soft_limits:
  joint1:
    soft_lower_limit: -6.10865238198  # -350 degrees
    soft_upper_limit: 6.10865238198   # +350 degrees
    k_position: 100.0
    k_velocity: 10.0

  joint2:
    soft_lower_limit: -2.18166156499  # -125 degrees
    soft_upper_limit: 2.18166156499   # +125 degrees
    k_position: 100.0
    k_velocity: 10.0

  joint3:
    soft_lower_limit: -2.18166156499  # -125 degrees
    soft_upper_limit: 2.18166156499   # +125 degrees
    k_position: 100.0
    k_velocity: 10.0

  joint4:
    soft_lower_limit: -6.10865238198  # -350 degrees
    soft_upper_limit: 6.10865238198   # +350 degrees
    k_position: 100.0
    k_velocity: 10.0

  joint5:
    soft_lower_limit: -2.18166156499  # -125 degrees
    soft_upper_limit: 2.18166156499   # +125 degrees
    k_position: 100.0
    k_velocity: 10.0

  joint6:
    soft_lower_limit: -6.10865238198  # -350 degrees
    soft_upper_limit: 6.10865238198   # +350 degrees
    k_position: 100.0
    k_velocity: 10.0
