# joint_limits.yaml allows the dynamics properties specified in the URDF to be overwritten or augmented as needed
# Specific joint properties can be changed with the keys [max_position, min_position, max_velocity, max_acceleration]
# Joint limits can be turned off with [has_velocity_limits, has_acceleration_limits]
joint_limits:
  joint1:
    has_velocity_limits: false
    max_velocity: 0
    has_acceleration_limits: false
    max_acceleration: 0
  joint2:
    has_velocity_limits: false
    max_velocity: 0
    has_acceleration_limits: false
    max_acceleration: 0
  joint3:
    has_velocity_limits: false
    max_velocity: 0
    has_acceleration_limits: false
    max_acceleration: 0
  joint4:
    has_velocity_limits: false
    max_velocity: 0
    has_acceleration_limits: false
    max_acceleration: 0
  joint5:
    has_velocity_limits: false
    max_velocity: 0
    has_acceleration_limits: false
    max_acceleration: 0
  joint6:
    has_velocity_limits: false
    max_velocity: 0
    has_acceleration_limits: false
    max_acceleration: 0