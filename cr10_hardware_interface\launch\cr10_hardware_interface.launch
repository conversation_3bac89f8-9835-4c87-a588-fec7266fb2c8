<?xml version="1.0"?>
<launch>
  <!-- Robot IP address -->
  <arg name="robot_ip" default="***********" doc="IP address of the CR10 robot"/>
  
  <!-- Control frequency -->
  <arg name="control_frequency" default="125.0" doc="Control loop frequency in Hz"/>
  
  <!-- Load robot description -->
  <param name="robot_description" command="$(find xacro)/xacro $(find dobot_description)/urdf/cr10_robot.urdf"/>
  
  <!-- Load joint controller configurations from YAML file -->
  <rosparam file="$(find cr10_hardware_interface)/config/cr10_controllers.yaml" command="load"/>
  
  <!-- Load joint limits -->
  <rosparam file="$(find cr10_hardware_interface)/config/cr10_joint_limits.yaml" command="load"/>
  
  <!-- Start the hardware interface node -->
  <node name="cr10_hardware_interface" pkg="cr10_hardware_interface" type="cr10_hardware_interface_node" output="screen">
    <param name="robot_ip" value="$(arg robot_ip)"/>
    <param name="control_frequency" value="$(arg control_frequency)"/>
    
    <!-- ServoJ parameters -->
    <param name="use_servo_j" value="true"/>
    <param name="servo_j_time" value="0.008"/>
    <param name="servo_j_lookahead_time" value="0.1"/>
    <param name="servo_j_gain" value="300.0"/>
    
    <!-- Control parameters -->
    <param name="position_tolerance" value="0.01"/>
    <param name="command_timeout" value="1.0"/>
  </node>
  
  <!-- Load and start the joint state controller -->
  <node name="controller_spawner" pkg="controller_manager" type="spawner" respawn="false"
        output="screen" args="joint_state_controller"/>
  
  <!-- Load and start the position controllers -->
  <node name="position_controller_spawner" pkg="controller_manager" type="spawner" respawn="false"
        output="screen" args="cr10_position_controller"/>
  
  <!-- Robot state publisher -->
  <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher" output="screen">
    <param name="publish_frequency" type="double" value="50.0"/>
  </node>
  
  <!-- Optional: Start RViz for visualization -->
  <arg name="rviz" default="false" doc="Start RViz for visualization"/>
  <node if="$(arg rviz)" name="rviz" pkg="rviz" type="rviz" 
        args="-d $(find cr10_hardware_interface)/config/cr10_visualization.rviz"/>
  
</launch>
