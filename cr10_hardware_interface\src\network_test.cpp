#include <ros/ros.h>
#include <iostream>
#include <sys/socket.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <chrono>

class NetworkTester
{
public:
    static bool testTcpConnection(const std::string& ip, int port, int timeout_ms = 3000)
    {
        int sock = socket(AF_INET, SOCK_STREAM, 0);
        if (sock < 0) {
            std::cout << "❌ Failed to create socket" << std::endl;
            return false;
        }
        
        // Set socket timeout
        struct timeval timeout;
        timeout.tv_sec = timeout_ms / 1000;
        timeout.tv_usec = (timeout_ms % 1000) * 1000;
        setsockopt(sock, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout));
        setsockopt(sock, SOL_SOCKET, SO_SNDTIMEO, &timeout, sizeof(timeout));
        
        struct sockaddr_in server_addr;
        server_addr.sin_family = AF_INET;
        server_addr.sin_port = htons(port);
        
        if (inet_pton(AF_INET, ip.c_str(), &server_addr.sin_addr) <= 0) {
            std::cout << "❌ Invalid IP address: " << ip << std::endl;
            close(sock);
            return false;
        }
        
        auto start = std::chrono::high_resolution_clock::now();
        int result = connect(sock, (struct sockaddr*)&server_addr, sizeof(server_addr));
        auto end = std::chrono::high_resolution_clock::now();
        
        close(sock);
        
        if (result == 0) {
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);
            std::cout << "✅ Port " << port << " - Connected (" << duration.count() << "ms)" << std::endl;
            return true;
        } else {
            std::cout << "❌ Port " << port << " - Connection failed" << std::endl;
            return false;
        }
    }
    
    static void testAllPorts(const std::string& ip)
    {
        std::cout << "\n=== Testing CR10 TCP Ports ===" << std::endl;
        std::cout << "Robot IP: " << ip << std::endl;
        
        struct PortInfo {
            int port;
            std::string description;
        };
        
        std::vector<PortInfo> ports = {
            {29999, "Dashboard/Control Port"},
            {30003, "Motion Command Port"},
            {30004, "Real-time Data Port"}
        };
        
        bool all_connected = true;
        
        for (const auto& port_info : ports) {
            std::cout << "\nTesting " << port_info.description << " (:" << port_info.port << ")..." << std::endl;
            bool connected = testTcpConnection(ip, port_info.port, 3000);
            if (!connected) {
                all_connected = false;
            }
        }
        
        std::cout << "\n" << std::string(50, '=') << std::endl;
        if (all_connected) {
            std::cout << "🎉 All ports accessible! Robot should be ready for connection." << std::endl;
        } else {
            std::cout << "⚠️ Some ports are not accessible. Check robot configuration." << std::endl;
        }
    }
    
    static void testPing(const std::string& ip)
    {
        std::cout << "\n=== Network Connectivity Test ===" << std::endl;
        std::cout << "Testing ping to " << ip << "..." << std::endl;
        
        std::string ping_cmd = "ping -c 3 -W 3 " + ip + " > /dev/null 2>&1";
        int result = system(ping_cmd.c_str());
        
        if (result == 0) {
            std::cout << "✅ Ping successful - Network connectivity OK" << std::endl;
        } else {
            std::cout << "❌ Ping failed - Check network configuration" << std::endl;
            std::cout << "   Possible issues:" << std::endl;
            std::cout << "   - Robot is not powered on" << std::endl;
            std::cout << "   - Network cable disconnected" << std::endl;
            std::cout << "   - Wrong IP address" << std::endl;
            std::cout << "   - Different network subnet" << std::endl;
        }
    }
    
    static void printNetworkInfo()
    {
        std::cout << "\n=== CR10 Network Configuration Info ===" << std::endl;
        std::cout << "Standard CR10 IP addresses:" << std::endl;
        std::cout << "  LAN1 (Ethernet): ***********" << std::endl;
        std::cout << "  LAN2 (Ethernet): *************" << std::endl;
        std::cout << "  WiFi: ***********" << std::endl;
        std::cout << "\nRequired TCP ports:" << std::endl;
        std::cout << "  29999 - Dashboard/Control commands" << std::endl;
        std::cout << "  30003 - Motion commands" << std::endl;
        std::cout << "  30004 - Real-time data feedback" << std::endl;
        std::cout << "\nTo configure your network interface:" << std::endl;
        std::cout << "  sudo ip addr add ***********00/24 dev eth0  # For LAN1" << std::endl;
        std::cout << "  sudo ip addr add *************00/24 dev eth0  # For LAN2" << std::endl;
    }
};

void printUsage()
{
    std::cout << "\nUsage:" << std::endl;
    std::cout << "  rosrun cr10_hardware_interface network_test [robot_ip]" << std::endl;
    std::cout << "\nExamples:" << std::endl;
    std::cout << "  rosrun cr10_hardware_interface network_test ***********" << std::endl;
    std::cout << "  rosrun cr10_hardware_interface network_test *************" << std::endl;
    std::cout << "\nDefault IP: ***********" << std::endl;
}

int main(int argc, char** argv)
{
    ros::init(argc, argv, "cr10_network_test");
    
    std::string robot_ip = "***********";  // Default IP
    
    // Parse command line arguments
    if (argc > 1) {
        robot_ip = argv[1];
    }
    
    // Validate IP format (basic check)
    if (robot_ip.find('.') == std::string::npos) {
        std::cout << "❌ Invalid IP address format: " << robot_ip << std::endl;
        printUsage();
        return -1;
    }
    
    std::cout << "CR10 Network Connectivity Test" << std::endl;
    std::cout << "==============================" << std::endl;
    
    // Print network configuration info
    NetworkTester::printNetworkInfo();
    
    // Test basic network connectivity
    NetworkTester::testPing(robot_ip);
    
    // Test TCP port connectivity
    NetworkTester::testAllPorts(robot_ip);
    
    std::cout << "\n=== Test Summary ===" << std::endl;
    std::cout << "If all tests pass, you can proceed with:" << std::endl;
    std::cout << "  rosrun cr10_hardware_interface test_connection " << robot_ip << std::endl;
    std::cout << "\nIf tests fail, check:" << std::endl;
    std::cout << "  1. Robot power and network connection" << std::endl;
    std::cout << "  2. Your computer's network configuration" << std::endl;
    std::cout << "  3. Robot controller TCP/IP settings" << std::endl;
    
    return 0;
}
