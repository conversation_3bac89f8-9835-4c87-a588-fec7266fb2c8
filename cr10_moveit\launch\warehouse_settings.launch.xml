<launch>
  <!-- Set the parameters for the warehouse and run the mongodb server. -->

  <!-- The default DB port for moveit (not default MongoDB port to avoid potential conflicts) -->
  <arg name="moveit_warehouse_port" default="33829" />

  <!-- The default DB host for moveit -->
  <arg name="moveit_warehouse_host" default="localhost" />

  <!-- Set parameters for the warehouse -->
  <param name="warehouse_port" value="$(arg moveit_warehouse_port)"/>
  <param name="warehouse_host" value="$(arg moveit_warehouse_host)"/>
  <param name="warehouse_exec" value="mongod" />
  <param name="warehouse_plugin" value="warehouse_ros_mongo::MongoDatabaseConnection" />

</launch>
