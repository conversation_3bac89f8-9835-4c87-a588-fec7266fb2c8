<launch>

  <!-- OMPL Plugin for MoveIt! -->
  <arg name="planning_plugin" value="ompl_interface/OMPLPlanner" />

  <!-- define capabilites that are loaded on start (space seperated) -->
  <arg name="capabilities" default=""/>

  <!-- inhibit capabilites (space seperated) -->
  <arg name="disable_capabilities" default=""/>

  <!-- The request adapters (plugins) used when planning with OMPL.
       ORDER MATTERS -->
  <arg name="planning_adapters" value="default_planner_request_adapters/AddTimeParameterization
				       default_planner_request_adapters/FixWorkspaceBounds
				       default_planner_request_adapters/FixStartStateBounds
				       default_planner_request_adapters/FixStartStateCollision
				       default_planner_request_adapters/FixStartStatePathConstraints" />

  <arg name="start_state_max_bounds_error" value="0.1" />

  <param name="planning_plugin" value="$(arg planning_plugin)" />
  <param name="request_adapters" value="$(arg planning_adapters)" />
  <param name="start_state_max_bounds_error" value="$(arg start_state_max_bounds_error)" />
  <param name="capabilities" value="$(arg capabilities)" />
  <param name="disable_capabilities" value="$(arg disable_capabilities)" />

  <rosparam command="load" file="$(find cr12_moveit)/config/ompl_planning.yaml"/>

</launch>
