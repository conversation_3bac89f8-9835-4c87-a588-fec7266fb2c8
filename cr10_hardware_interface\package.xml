<?xml version="1.0"?>
<package format="2">
  <name>cr10_hardware_interface</name>
  <version>1.0.0</version>
  <description>Hardware interface for CR10 robot using ROS control</description>

  <maintainer email="<EMAIL>">Dobot Developer</maintainer>
  <license>BSD</license>

  <buildtool_depend>catkin</buildtool_depend>

  <!-- Build dependencies -->
  <build_depend>roscpp</build_depend>
  <build_depend>hardware_interface</build_depend>
  <build_depend>controller_manager</build_depend>
  <build_depend>joint_state_controller</build_depend>
  <build_depend>position_controllers</build_depend>
  <build_depend>trajectory_msgs</build_depend>
  <build_depend>control_msgs</build_depend>
  <build_depend>sensor_msgs</build_depend>
  <build_depend>std_msgs</build_depend>
  <build_depend>dobot_bringup</build_depend>

  <!-- Runtime dependencies -->
  <exec_depend>roscpp</exec_depend>
  <exec_depend>hardware_interface</exec_depend>
  <exec_depend>controller_manager</exec_depend>
  <exec_depend>joint_state_controller</exec_depend>
  <exec_depend>position_controllers</exec_depend>
  <exec_depend>trajectory_msgs</exec_depend>
  <exec_depend>control_msgs</exec_depend>
  <exec_depend>sensor_msgs</exec_depend>
  <exec_depend>std_msgs</exec_depend>
  <exec_depend>dobot_bringup</exec_depend>

  <export>
  </export>
</package>
