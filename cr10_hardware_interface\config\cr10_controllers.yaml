# Joint state controller
joint_state_controller:
  type: joint_state_controller/JointStateController
  publish_rate: 50

# Position controllers for individual joints
cr10_position_controller:
  type: position_controllers/JointTrajectoryController
  joints:
    - joint1
    - joint2
    - joint3
    - joint4
    - joint5
    - joint6
  
  constraints:
    goal_time: 0.5
    stopped_velocity_tolerance: 0.02
    joint1:
      trajectory: 0.05
      goal: 0.02
    joint2:
      trajectory: 0.05
      goal: 0.02
    joint3:
      trajectory: 0.05
      goal: 0.02
    joint4:
      trajectory: 0.05
      goal: 0.02
    joint5:
      trajectory: 0.05
      goal: 0.02
    joint6:
      trajectory: 0.05
      goal: 0.02

# Individual joint position controllers (alternative)
joint1_position_controller:
  type: position_controllers/JointPositionController
  joint: joint1
  pid: {p: 100.0, i: 0.01, d: 10.0}

joint2_position_controller:
  type: position_controllers/JointPositionController
  joint: joint2
  pid: {p: 100.0, i: 0.01, d: 10.0}

joint3_position_controller:
  type: position_controllers/JointPositionController
  joint: joint3
  pid: {p: 100.0, i: 0.01, d: 10.0}

joint4_position_controller:
  type: position_controllers/JointPositionController
  joint: joint4
  pid: {p: 100.0, i: 0.01, d: 10.0}

joint5_position_controller:
  type: position_controllers/JointPositionController
  joint: joint5
  pid: {p: 100.0, i: 0.01, d: 10.0}

joint6_position_controller:
  type: position_controllers/JointPositionController
  joint: joint6
  pid: {p: 100.0, i: 0.01, d: 10.0}

# Velocity controllers (optional)
joint1_velocity_controller:
  type: velocity_controllers/JointVelocityController
  joint: joint1

joint2_velocity_controller:
  type: velocity_controllers/JointVelocityController
  joint: joint2

joint3_velocity_controller:
  type: velocity_controllers/JointVelocityController
  joint: joint3

joint4_velocity_controller:
  type: velocity_controllers/JointVelocityController
  joint: joint4

joint5_velocity_controller:
  type: velocity_controllers/JointVelocityController
  joint: joint5

joint6_velocity_controller:
  type: velocity_controllers/JointVelocityController
  joint: joint6
