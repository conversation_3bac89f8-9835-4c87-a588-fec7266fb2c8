<?xml version="1.0"?>
<launch>
  <!-- Robot IP address -->
  <arg name="robot_ip" default="***********" doc="IP address of the CR10 robot"/>
  
  <!-- Test type -->
  <arg name="test_type" default="connection" doc="Type of test: network, connection, or full"/>
  
  <!-- Load robot description for testing -->
  <param name="robot_description" command="$(find xacro)/xacro $(find dobot_description)/urdf/cr10_robot.urdf"/>
  
  <!-- Network connectivity test -->
  <group if="$(eval arg('test_type') == 'network')">
    <node name="network_test" pkg="cr10_hardware_interface" type="network_test" 
          args="$(arg robot_ip)" output="screen"/>
  </group>
  
  <!-- Connection test -->
  <group if="$(eval arg('test_type') == 'connection')">
    <node name="connection_test" pkg="cr10_hardware_interface" type="test_connection" 
          args="$(arg robot_ip)" output="screen"/>
  </group>
  
  <!-- Full hardware interface test -->
  <group if="$(eval arg('test_type') == 'full')">
    <!-- Load test parameters -->
    <rosparam>
      robot_ip: $(arg robot_ip)
      control_frequency: 10.0
      use_servo_j: true
      position_tolerance: 0.01
      servo_j_time: 0.008
      servo_j_lookahead_time: 0.1
      servo_j_gain: 300.0
      command_timeout: 1.0
    </rosparam>
    
    <!-- Start hardware interface for testing -->
    <node name="cr10_hardware_interface_test" pkg="cr10_hardware_interface" 
          type="cr10_hardware_interface_node" output="screen">
      <param name="robot_ip" value="$(arg robot_ip)"/>
      <param name="control_frequency" value="10.0"/>
    </node>
    
    <!-- Joint state publisher for testing -->
    <node name="joint_state_publisher" pkg="joint_state_publisher" type="joint_state_publisher">
      <param name="use_gui" value="false"/>
    </node>
    
    <!-- Robot state publisher -->
    <node name="robot_state_publisher" pkg="robot_state_publisher" type="robot_state_publisher"/>
    
    <!-- Optional: RViz for visualization -->
    <arg name="rviz" default="false"/>
    <node if="$(arg rviz)" name="rviz" pkg="rviz" type="rviz" 
          args="-d $(find cr10_hardware_interface)/config/cr10_visualization.rviz"/>
  </group>
  
</launch>
