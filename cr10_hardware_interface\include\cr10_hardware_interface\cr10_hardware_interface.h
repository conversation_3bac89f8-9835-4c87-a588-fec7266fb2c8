#ifndef CR10_HARDWARE_INTERFACE_H
#define CR10_HARDWARE_INTERFACE_H

#include <ros/ros.h>
#include <hardware_interface/joint_command_interface.h>
#include <hardware_interface/joint_state_interface.h>
#include <hardware_interface/robot_hw.h>
#include <controller_manager/controller_manager.h>
#include <joint_limits_interface/joint_limits.h>
#include <joint_limits_interface/joint_limits_interface.h>
#include <joint_limits_interface/joint_limits_rosparam.h>
#include <joint_limits_interface/joint_limits_urdf.h>

#include <dobot_bringup/commander.h>
#include <memory>
#include <vector>
#include <string>
#include <mutex>

namespace cr10_hardware_interface
{

/**
 * @brief Hardware interface for CR10 robot
 * 
 * This class implements the ROS control hardware interface for the CR10 robot,
 * providing joint state feedback and position control capabilities.
 */
class CR10HardwareInterface : public hardware_interface::RobotHW
{
public:
    /**
     * @brief Constructor
     * @param nh ROS node handle
     */
    explicit CR10HardwareInterface(ros::NodeHandle& nh);

    /**
     * @brief Destructor
     */
    virtual ~CR10HardwareInterface();

    /**
     * @brief Initialize the hardware interface
     * @return true if initialization successful, false otherwise
     */
    bool init();

    /**
     * @brief Read joint states from the robot
     * @param time Current time
     * @param period Time since last read
     */
    void read(const ros::Time& time, const ros::Duration& period) override;

    /**
     * @brief Write joint commands to the robot
     * @param time Current time
     * @param period Time since last write
     */
    void write(const ros::Time& time, const ros::Duration& period) override;

    /**
     * @brief Check if the robot is connected
     * @return true if connected, false otherwise
     */
    bool isConnected() const;

    /**
     * @brief Check if the robot is enabled
     * @return true if enabled, false otherwise
     */
    bool isEnabled() const;

    /**
     * @brief Enable the robot
     */
    void enableRobot();

    /**
     * @brief Disable the robot
     */
    void disableRobot();

    /**
     * @brief Clear robot errors
     */
    void clearError();

private:
    // ROS node handle
    ros::NodeHandle nh_;

    // Robot commander for communication
    std::shared_ptr<CR5Commander> commander_;

    // Joint names
    std::vector<std::string> joint_names_;

    // Joint state data
    std::vector<double> joint_position_;
    std::vector<double> joint_velocity_;
    std::vector<double> joint_effort_;

    // Joint command data
    std::vector<double> joint_position_command_;
    std::vector<double> joint_position_command_prev_;

    // Hardware interfaces
    hardware_interface::JointStateInterface joint_state_interface_;
    hardware_interface::PositionJointInterface position_joint_interface_;

    // Joint limits interfaces
    joint_limits_interface::PositionJointSaturationInterface position_joint_limits_interface_;
    joint_limits_interface::PositionJointSoftLimitsInterface position_joint_soft_limits_interface_;

    // Robot configuration
    std::string robot_ip_;
    double control_frequency_;
    double position_tolerance_;
    bool use_servo_j_;
    double servo_j_time_;
    double servo_j_lookahead_time_;
    double servo_j_gain_;

    // Control state
    bool initialized_;
    bool robot_connected_;
    bool robot_enabled_;
    std::mutex state_mutex_;

    // Timing
    ros::Time last_command_time_;
    ros::Duration command_timeout_;

    /**
     * @brief Load parameters from ROS parameter server
     * @return true if successful, false otherwise
     */
    bool loadParameters();

    /**
     * @brief Register hardware interfaces
     * @return true if successful, false otherwise
     */
    bool registerInterfaces();

    /**
     * @brief Setup joint limits
     * @return true if successful, false otherwise
     */
    bool setupJointLimits();

    /**
     * @brief Check if joint commands have changed significantly
     * @return true if commands changed, false otherwise
     */
    bool commandsChanged();

    /**
     * @brief Send joint position commands to robot using ServoJ
     */
    void sendServoJCommand();

    /**
     * @brief Validate joint positions are within limits
     * @param positions Joint positions to validate
     * @return true if valid, false otherwise
     */
    bool validateJointPositions(const std::vector<double>& positions);

    /**
     * @brief Convert radians to degrees
     * @param rad Angle in radians
     * @return Angle in degrees
     */
    inline double rad2deg(double rad) const { return rad * 180.0 / M_PI; }

    /**
     * @brief Convert degrees to radians
     * @param deg Angle in degrees
     * @return Angle in radians
     */
    inline double deg2rad(double deg) const { return deg * M_PI / 180.0; }
};

} // namespace cr10_hardware_interface

#endif // CR10_HARDWARE_INTERFACE_H
